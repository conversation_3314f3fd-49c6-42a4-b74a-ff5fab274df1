<?php

namespace App\Console\Commands;

use App\Models\AccountType;
use Illuminate\Console\Command;

class SyncMaterialPositions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'materials:sync-positions {--account-type-id= : Sync positions for a specific account type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync material positions for account types based on their lesson lists';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $accountTypeId = $this->option('account-type-id');

        if ($accountTypeId) {
            $accountType = AccountType::find($accountTypeId);
            if (!$accountType) {
                $this->error("Account type with ID {$accountTypeId} not found.");
                return 1;
            }
            $this->syncAccountType($accountType);
        } else {
            $accountTypes = AccountType::all();
            $this->info("Syncing material positions for " . $accountTypes->count() . " account types...");
            
            foreach ($accountTypes as $accountType) {
                $this->syncAccountType($accountType);
            }
        }

        $this->info("Material positions sync completed!");
        return 0;
    }

    /**
     * Sync material positions for a specific account type
     *
     * @param AccountType $accountType
     * @return void
     */
    private function syncAccountType(AccountType $accountType)
    {
        $this->info("Syncing positions for account type: {$accountType->name} (ID: {$accountType->id})");
        
        try {
            $accountType->syncMaterialPositions();
            $this->info("✓ Synced positions for account type: {$accountType->name}");
        } catch (\Exception $e) {
            $this->error("✗ Failed to sync positions for account type: {$accountType->name}");
            $this->error("Error: " . $e->getMessage());
        }
    }
}
