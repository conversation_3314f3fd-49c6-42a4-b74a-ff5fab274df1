<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\Material;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class MaterialController extends Controller
{
    /**
     * Get materials for an account with prefixed titles based on account type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAccountMaterials(Request $request): JsonResponse
    {
        $token = $request->input('token');
        $softwareType = $request->input('software_type');

        if (!$token || !isset($softwareType)) {
            return response()->json([
                'error' => true,
                'message' => 'Token and software_type are required'
            ], 400);
        }

        // Find the account using the same logic as ValidTokenController
        $account = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where(function ($q) use ($softwareType){
                $q->where(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_MAMNON)
                        ->where('is_mamnon', 1);
                })
                ->orWhere(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_TIEUHOC)
                        ->where('is_tieuhoc', 1);
                });
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNull('deleted_at')
            ->first();

        if (!$account) {
            return response()->json([
                'error' => true,
                'message' => 'Account not found or invalid'
            ], 404);
        }

        // Check if account is expired
        $accountActive = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNotNull('time_expired')
            ->whereDate('time_expired', '>', now())
            ->whereNull('deleted_at')
            ->first();

        if (!$accountActive) {
            return response()->json([
                'error' => true,
                'message' => 'Account expired'
            ], 403);
        }

        // Get the appropriate account type
        $accountType = null;
        if ($softwareType == Account::SOFTWARE_MAMNON) {
            $accountType = AccountType::find($accountActive->mamnon_account_type_id);
        } elseif ($softwareType == Account::SOFTWARE_TIEUHOC) {
            $accountType = AccountType::find($accountActive->tieuhoc_account_type_id);
        }

        if (!$accountType) {
            return response()->json([
                'error' => true,
                'message' => 'Account type not found'
            ], 404);
        }

        // Get materials with prefixes for this account type
        $materials = $accountType->getMaterialsWithPrefixes();

        // Format the response
        $formattedMaterials = $materials->map(function ($material) {
            return [
                'id' => $material->id,
                'title' => $material->prefixed_title,
                'original_title' => $material->original_title,
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'position' => $material->position,
                'lesson' => [
                    'id' => $material->lesson_id,
                    'title' => $material->lesson['title'] ?? null,
                ],
                'course' => [
                    'id' => $material->lesson['course']['id'] ?? null,
                    'title' => $material->lesson['course']['title_en'] ?? null,
                ],
                'created_at' => $material->created_at,
                'updated_at' => $material->updated_at,
            ];
        });

        return response()->json([
            'error' => false,
            'data' => [
                'account_type' => [
                    'id' => $accountType->id,
                    'name' => $accountType->name,
                    'code' => $accountType->code,
                ],
                'materials' => $formattedMaterials,
                'total_materials' => $formattedMaterials->count(),
            ]
        ]);
    }

    /**
     * Get a specific material with prefix for an account
     *
     * @param Request $request
     * @param int $materialId
     * @return JsonResponse
     */
    public function getAccountMaterial(Request $request, $materialId): JsonResponse
    {
        $token = $request->input('token');
        $softwareType = $request->input('software_type');

        if (!$token || !isset($softwareType)) {
            return response()->json([
                'error' => true,
                'message' => 'Token and software_type are required'
            ], 400);
        }

        // Find the account (same logic as above)
        $account = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where(function ($q) use ($softwareType){
                $q->where(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_MAMNON)
                        ->where('is_mamnon', 1);
                })
                ->orWhere(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_TIEUHOC)
                        ->where('is_tieuhoc', 1);
                });
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNull('deleted_at')
            ->first();

        if (!$account) {
            return response()->json([
                'error' => true,
                'message' => 'Account not found or invalid'
            ], 404);
        }

        // Get the appropriate account type
        $accountType = null;
        if ($softwareType == Account::SOFTWARE_MAMNON) {
            $accountType = AccountType::find($account->mamnon_account_type_id);
        } elseif ($softwareType == Account::SOFTWARE_TIEUHOC) {
            $accountType = AccountType::find($account->tieuhoc_account_type_id);
        }

        if (!$accountType) {
            return response()->json([
                'error' => true,
                'message' => 'Account type not found'
            ], 404);
        }

        // Get the specific material with prefix
        $material = $accountType->getMaterialWithPrefix($materialId);

        if (!$material) {
            return response()->json([
                'error' => true,
                'message' => 'Material not found or not accessible for this account type'
            ], 404);
        }

        return response()->json([
            'error' => false,
            'data' => [
                'id' => $material->id,
                'title' => $material->prefixed_title,
                'original_title' => $material->original_title,
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'position' => $material->position,
                'lesson' => [
                    'id' => $material->lesson_id,
                    'title' => $material->lesson['title'] ?? null,
                ],
                'course' => [
                    'id' => $material->lesson['course']['id'] ?? null,
                    'title' => $material->lesson['course']['title_en'] ?? null,
                ],
                'created_at' => $material->created_at,
                'updated_at' => $material->updated_at,
            ]
        ]);
    }
}
