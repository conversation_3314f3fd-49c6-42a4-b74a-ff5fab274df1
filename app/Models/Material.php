<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['lesson_id', 'title', 'type', 'content', 'content_url', 'deleted_at', 'document_url'];

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the material positions for different account types
     */
    public function accountTypePositions()
    {
        return $this->hasMany(AccountTypeMaterialPosition::class);
    }

    /**
     * Get the prefixed title for a specific account type
     *
     * @param int $accountTypeId
     * @return string
     */
    public function getPrefixedTitle($accountTypeId)
    {
        $position = AccountTypeMaterialPosition::getMaterialPosition($accountTypeId, $this->id);

        if (!$position) {
            // If no position exists, this material is not assigned to this account type
            return $this->title;
        }

        return "Bài " . $position . " - " . $this->title;
    }

    /**
     * Get the position for a specific account type
     *
     * @param int $accountTypeId
     * @return int|null
     */
    public function getPositionForAccountType($accountTypeId)
    {
        return AccountTypeMaterialPosition::getMaterialPosition($accountTypeId, $this->id);
    }
}
