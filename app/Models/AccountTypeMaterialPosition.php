<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountTypeMaterialPosition extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_type_id',
        'material_id',
        'position',
    ];

    /**
     * Get the account type that owns this position.
     */
    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }

    /**
     * Get the material that owns this position.
     */
    public function material()
    {
        return $this->belongsTo(Material::class);
    }

    /**
     * Get the next available position for an account type
     *
     * @param int $accountTypeId
     * @return int
     */
    public static function getNextPosition($accountTypeId)
    {
        $maxPosition = self::where('account_type_id', $accountTypeId)->max('position');
        return $maxPosition ? $maxPosition + 1 : 1;
    }

    /**
     * Assign a position to a material for an account type
     *
     * @param int $accountTypeId
     * @param int $materialId
     * @return AccountTypeMaterialPosition
     */
    public static function assignPosition($accountTypeId, $materialId)
    {
        // Check if position already exists
        $existing = self::where('account_type_id', $accountTypeId)
                       ->where('material_id', $materialId)
                       ->first();

        if ($existing) {
            return $existing;
        }

        // Get next available position
        $position = self::getNextPosition($accountTypeId);

        // Create new position record
        return self::create([
            'account_type_id' => $accountTypeId,
            'material_id' => $materialId,
            'position' => $position,
        ]);
    }

    /**
     * Get material position for a specific account type
     *
     * @param int $accountTypeId
     * @param int $materialId
     * @return int|null
     */
    public static function getMaterialPosition($accountTypeId, $materialId)
    {
        $position = self::where('account_type_id', $accountTypeId)
                       ->where('material_id', $materialId)
                       ->first();

        return $position ? $position->position : null;
    }
}
